// src/features/users/ClientSideUserTable.jsx

import { userColumns } from "./UserTableDefs";
import { CommonTable } from "../../components/table/CommonTable";
import { sampleUserData } from "./sampleData"; // An array of 100 user objects

export default function ClientSideUserTable({
  enableRowContextMenu,
  renderRowContextMenu,
}) {
  const data = sampleUserData;

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Users (Client-Side)</h1>
      <CommonTable
        data={data}
        columns={userColumns}
        paginationMode="client"
        enableGlobalFilter={true}
        enableRowSelection={true}
        fixedHeader={true}
        toolbarContent={
          <button className="bg-indigo-600 text-white px-3 py-1.5 rounded-md hover:bg-indigo-700">
            Add User
          </button>
        }
        enableRowContextMenu={enableRowContextMenu}
        renderRowContextMenu={renderRowContextMenu}
      />
    </div>
  );
}
