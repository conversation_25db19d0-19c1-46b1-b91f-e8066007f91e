// src/features/users/ServerSideUserTable.jsx
import React, { useState, useEffect } from "react";
import { userColumns } from "./UserTableDefs";
import { CommonTable } from "../../components/table/CommonTable";

// --- Mock API Fetch Function ---
const fetchServerData = async (pagination, sorting) => {
  console.log("Fetching data with:", { pagination, sorting });
  await new Promise((resolve) => setTimeout(resolve, 500));
  const pageCount = 10;
  const data = Array.from({ length: pagination.pageSize }, (_, i) => ({
    id: pagination.pageIndex * pagination.pageSize + i + 1,
    name: `User ${pagination.pageIndex * pagination.pageSize + i + 1}`,
    email: `user${
      pagination.pageIndex * pagination.pageSize + i + 1
    }@example.com`,
    role: "User",
    status: i % 2 === 0 ? "Active" : "Inactive",
    createdAt: new Date().toISOString(),
  }));
  return { data, pageCount };
};
// --- End Mock API ---

export default function ServerSideUserTable() {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [pageCount, setPageCount] = useState(0);

  // State for server-side operations
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [sorting, setSorting] = useState([]);
  const [rowSelection, setRowSelection] = useState({});

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      const { data: fetchedData, pageCount: fetchedPageCount } =
        await fetchServerData(pagination, sorting);
      setData(fetchedData);
      setPageCount(fetchedPageCount);
      setIsLoading(false);
    };
    fetchData();
  }, [pagination, sorting]); // Re-fetch when pagination or sorting changes

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Users (Server-Side)</h1>
      <CommonTable
        data={data}
        columns={userColumns}
        paginationMode="server"
        // Pass state and handlers for server-side control
        pageCount={pageCount}
        pagination={pagination}
        onPaginationChange={setPagination}
        sorting={sorting}
        onSortingChange={setSorting}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        isLoading={isLoading}
        fixedHeader={true}
        enableRowSelection={true}
      />
    </div>
  );
}
