// src/components/table/constants/tableConstants.js

/**
 * Constants used throughout the table components
 */

export const DEFAULT_PAGE_SIZES = [10, 20, 30, 40, 50];

export const DEFAULT_PAGINATION = {
  pageIndex: 0,
  pageSize: 10,
};

export const PAGINATION_MODES = {
  CLIENT: "client",
  SERVER: "server",
};

export const TABLE_CLASSES = {
  container: "flex flex-col gap-4",
  tableWrapper: "rounded-lg border dark:border-gray-700 shadow-sm",
  scrollContainer: "overflow-auto relative scrollbar-thin scrollbar-track-transparent scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500",
  fixedHeaderContainer: "max-h-[600px]",
  table: "w-full border-collapse text-left",
  thead: "text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50",
  fixedHeader: "sticky top-0 z-20",
  th: "px-4 py-3 font-medium whitespace-nowrap bg-gray-50 dark:bg-gray-800/50",
  sortableHeader: "cursor-pointer select-none",
  headerContent: "flex items-center gap-2",
  sortIcon: "h-3 w-3 text-gray-400",
};

export const TOOLBAR_CLASSES = {
  container: "flex items-center justify-between",
  leftSection: "flex items-center gap-2",
  searchContainer: "relative",
  searchIcon: "absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400",
  searchInput: "pl-9 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 focus:ring-2 focus:ring-indigo-500 focus:outline-none text-sm",
};

export const PAGINATION_CLASSES = {
  container: "flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 pt-2",
  selectionInfo: "flex-1",
  controls: "flex items-center gap-6",
  pageSizeContainer: "flex items-center gap-2",
  pageSizeSelect: "border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-white dark:bg-gray-700 focus:ring-1 focus:ring-indigo-500 focus:outline-none",
  pageInfo: "font-semibold text-gray-800 dark:text-gray-200",
  buttonContainer: "flex items-center gap-1",
  button: "p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent",
  buttonIcon: "h-4 w-4",
};

export const CONTEXT_MENU_CLASSES = {
  container: "fixed z-50",
};
