// src/components/table/hooks/useTableState.js

import { useState, useMemo } from "react";

/**
 * Custom hook to manage table state (pagination, sorting, selection, filtering)
 * Handles both client-side and server-side state management
 */
export function useTableState({
  paginationMode = "client",
  controlledPagination,
  setControlledPagination,
  controlledSorting,
  setControlledSorting,
  controlledRowSelection,
  setControlledRowSelection,
}) {
  // Internal state for client-side operations
  const [globalFilter, setGlobalFilter] = useState("");
  const [internalSorting, setInternalSorting] = useState([]);
  const [internalPagination, setInternalPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [internalRowSelection, setInternalRowSelection] = useState({});

  // Determine which state and handlers to use (controlled vs. internal)
  const isServerPagination = paginationMode === "server";
  const isServerSorting = !!setControlledSorting;
  const isServerSelection = !!setControlledRowSelection;

  const tableState = useMemo(() => ({
    pagination: isServerPagination ? controlledPagination : internalPagination,
    onPaginationChange: isServerPagination ? setControlledPagination : setInternalPagination,
    sorting: isServerSorting ? controlledSorting : internalSorting,
    onSortingChange: isServerSorting ? setControlledSorting : setInternalSorting,
    rowSelection: isServerSelection ? controlledRowSelection : internalRowSelection,
    onRowSelectionChange: isServerSelection ? setControlledRowSelection : setInternalRowSelection,
    globalFilter,
    onGlobalFilterChange: setGlobalFilter,
    isServerPagination,
    isServerSorting,
    isServerSelection,
  }), [
    isServerPagination,
    controlledPagination,
    internalPagination,
    setControlledPagination,
    isServerSorting,
    controlledSorting,
    internalSorting,
    setControlledSorting,
    isServerSelection,
    controlledRowSelection,
    internalRowSelection,
    setControlledRowSelection,
    globalFilter,
  ]);

  return tableState;
}
