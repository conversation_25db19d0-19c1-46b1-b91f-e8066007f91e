// src/components/table/components/ContextMenu.jsx

import React from "react";
import PropTypes from "prop-types";
import { CONTEXT_MENU_CLASSES } from "../constants/tableConstants";

/**
 * Context menu component for table rows
 */
export function ContextMenu({ contextMenu, renderRowContextMenu, closeMenu }) {
  if (!contextMenu.visible || !contextMenu.row) {
    return null;
  }

  return (
    <div
      style={{ top: contextMenu.y, left: contextMenu.x }}
      className={CONTEXT_MENU_CLASSES.container}
      // Stop propagation to prevent the 'click outside' handler from closing the menu immediately
      onClick={(e) => e.stopPropagation()}
    >
      {/* Call the user-provided render prop */}
      {renderRowContextMenu(contextMenu.row, closeMenu)}
    </div>
  );
}

ContextMenu.propTypes = {
  contextMenu: PropTypes.shape({
    visible: PropTypes.bool,
    x: PropTypes.number,
    y: PropTypes.number,
    row: PropTypes.object,
  }).isRequired,
  renderRowContextMenu: PropTypes.func.isRequired,
  closeMenu: PropTypes.func.isRequired,
};
