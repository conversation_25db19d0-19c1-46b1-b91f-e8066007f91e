// src/components/table/hooks/useContextMenu.js

import { useState, useEffect } from "react";

/**
 * Custom hook to manage context menu state and behavior
 */
export function useContextMenu(enableRowContextMenu = false) {
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    row: null,
  });

  const handleContextMenu = (event, row) => {
    if (!enableRowContextMenu) return;
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      row: row,
    });
  };

  const closeMenu = () => {
    setContextMenu((prev) => ({ ...prev, visible: false }));
  };

  // Effect to handle closing the menu when clicking outside or pressing Escape
  useEffect(() => {
    if (!contextMenu.visible) return;

    const handleClickOutside = () => closeMenu();
    const handleEscape = (e) => {
      if (e.key === "Escape") closeMenu();
    };

    document.addEventListener("click", handleClickOutside);
    document.addEventListener("keydown", handleEscape);

    return () => {
      document.removeEventListener("click", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [contextMenu.visible]);

  return {
    contextMenu,
    handleContextMenu,
    closeMenu,
  };
}
