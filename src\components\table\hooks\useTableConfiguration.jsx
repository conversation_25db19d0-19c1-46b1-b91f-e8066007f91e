// src/components/table/hooks/useTableConfiguration.js

import { useState, useMemo } from "react";
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
} from "@tanstack/react-table";

/**
 * Custom hook to configure and create the react-table instance
 * Handles column processing, pinning, and table configuration
 */
export function useTableConfiguration({
  data,
  columns,
  enableRowSelection,
  tableState,
  controlledPageCount,
}) {
  // STEP 1: Create the single, authoritative list of columns.
  const finalColumns = useMemo(() => {
    if (!enableRowSelection) return columns;
    const selectionColumn = {
      id: "select",
      meta: { isPinned: "left" },
      size: 45,
      header: ({ table }) => (
        <input
          type="checkbox"
          className="h-4 w-4"
          checked={table.getIsAllPageRowsSelected()}
          onChange={table.getToggleAllPageRowsSelectedHandler()}
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          className="h-4 w-4"
          checked={row.getIsSelected()}
          onChange={row.getToggleSelectedHandler()}
        />
      ),
    };
    return [selectionColumn, ...columns];
  }, [columns, enableRowSelection]);

  // STEP 2: Derive the pinning state from the final, authoritative column list.
  const [columnPinning, setColumnPinning] = useState(() => {
    const left = finalColumns
      .filter((c) => c.meta?.isPinned === "left")
      .map((c) => c.id || c.accessorKey);
    const right = finalColumns
      .filter((c) => c.meta?.isPinned === "right")
      .map((c) => c.id || c.accessorKey);
    return { left, right };
  });

  const table = useReactTable({
    data,
    columns: finalColumns,
    pageCount: controlledPageCount,
    state: {
      globalFilter: tableState.globalFilter,
      sorting: tableState.sorting,
      pagination: tableState.pagination,
      rowSelection: tableState.rowSelection,
      columnPinning,
    },
    // Models
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // Handlers
    onGlobalFilterChange: tableState.onGlobalFilterChange,
    onSortingChange: tableState.onSortingChange,
    onPaginationChange: tableState.onPaginationChange,
    onRowSelectionChange: tableState.onRowSelectionChange,
    onColumnPinningChange: setColumnPinning,
    // Options
    enableColumnPinning: true,
    manualPagination: tableState.isServerPagination,
    manualSorting: tableState.isServerSorting,
  });

  return {
    table,
    finalColumns,
    columnPinning,
  };
}
