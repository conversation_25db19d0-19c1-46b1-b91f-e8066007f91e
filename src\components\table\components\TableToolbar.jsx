// src/components/table/components/TableToolbar.jsx

import React from "react";
import PropTypes from "prop-types";
import { Search } from "lucide-react";
import { TOOLBAR_CLASSES } from "../constants/tableConstants";

/**
 * Table toolbar component with search and custom content
 */
export function TableToolbar({
  enableGlobalFilter,
  globalFilter,
  onGlobalFilterChange,
  toolbarContent,
}) {
  if (!enableGlobalFilter && !toolbarContent) {
    return null;
  }

  return (
    <div className={TOOLBAR_CLASSES.container}>
      <div className={TOOLBAR_CLASSES.leftSection}>
        {enableGlobalFilter && (
          <div className={TOOLBAR_CLASSES.searchContainer}>
            <Search className={TOOLBAR_CLASSES.searchIcon} />
            <input
              type="text"
              value={globalFilter ?? ""}
              onChange={(e) => onGlobalFilterChange(e.target.value)}
              placeholder="Search..."
              className={TOOLBAR_CLASSES.searchInput}
            />
          </div>
        )}
      </div>
      {toolbarContent}
    </div>
  );
}

TableToolbar.propTypes = {
  enableGlobalFilter: PropTypes.bool,
  globalFilter: PropTypes.string,
  onGlobalFilterChange: PropTypes.func,
  toolbarContent: PropTypes.node,
};
