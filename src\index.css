/* src/index.css */

/*
  This is the main entry point for Tailwind v4.
  It automatically includes base, components, and utilities.
*/
@import "tailwindcss";

/*
  This is where we define custom utilities that were previously
  handled by plugins in tailwind.config.js
*/
@layer utilities {
  .shadow-pinned-right,
  .shadow-pinned-left {
    position: relative; /* Prerequisite for positioning the ::after element */
  }

  /* The actual shadow for the LAST left-pinned column */
  .shadow-pinned-right::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: -1px; /* Extend 1px to cover the bottom border */
    left: 100%; /* Position it just to the right of the cell */
    width: 10px; /* The width of our shadow effect */
    pointer-events: none; /* Make sure the shadow is not clickable */
    background: linear-gradient(
      to right,
      rgba(0, 0, 0, 0.1),
      /* Darker at the edge */ rgba(0, 0, 0, 0) /* Fades to transparent */
    );
  }

  /* The actual shadow for the FIRST right-pinned column */
  .shadow-pinned-left::after {
    content: "";
    position: absolute;
    top: 0;
    bottom: -1px;
    right: 100%; /* Position it just to the left of the cell */
    width: 10px;
    pointer-events: none;
    background: linear-gradient(to left, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0));
  }

  /* 2. Custom Scrollbar Utilities (replaces tailwind-scrollbar) */
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  .scrollbar-track-transparent {
    scrollbar-color: var(--tw-scrollbar-thumb) transparent;
  }
  .scrollbar-thumb-gray-300 {
    --tw-scrollbar-thumb: #d1d5db; /* theme('colors.gray.300') */
  }
  .dark .scrollbar-thumb-gray-600 {
    --tw-scrollbar-thumb: #4b5563; /* theme('colors.gray.600') */
  }
  .hover\:scrollbar-thumb-gray-400:hover {
    --tw-scrollbar-thumb: #9ca3af; /* theme('colors.gray.400') */
  }
  .dark .hover\:scrollbar-thumb-gray-500:hover {
    --tw-scrollbar-thumb: #6b7280; /* theme('colors.gray.500') */
  }

  /* For Webkit-based browsers (Chrome, Safari, Edge) */
  @media (pointer: fine) {
    .scrollbar-thin::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    .scrollbar-track-transparent::-webkit-scrollbar-track {
      background-color: transparent;
    }
    .scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
      background-color: #d1d5db;
      border-radius: 9999px;
    }
    .dark .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
      background-color: #4b5563;
    }
    .hover\:scrollbar-thumb-gray-400::-webkit-scrollbar-thumb:hover {
      background-color: #9ca3af;
    }
    .dark .hover\:scrollbar-thumb-gray-500::-webkit-scrollbar-thumb:hover {
      background-color: #6b7280;
    }
  }
}

/* 3. Animations (replaces tailwindcss-animate) */
@theme {
  --animation-duration-150: 150ms;
  --animation-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pulse {
  50% {
    opacity: 0.5;
  }
}

@layer base {
  .animate-pulse {
    animation: pulse 2s var(--animation-ease-in-out) infinite;
  }
}
