// src/components/ui/CommonTable.jsx

import React, { useState, useMemo, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import {
  useReactTable,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  flexRender,
} from "@tanstack/react-table";
import {
  ArrowUpDown,
  Search,
  ChevronsLeft,
  ChevronLeft,
  ChevronRight,
  ChevronsRight,
} from "lucide-react";

/**
 * A highly reusable and configurable table component built with @tanstack/react-table and Tailwind CSS.
 * It supports client/server-side pagination, sorting, global filtering, row selection, and column pinning.
 */
export function CommonTable({
  data,
  columns,
  paginationMode = "client",
  enableGlobalFilter = false,
  enableRowSelection = false,
  fixedHeader = false,
  toolbarContent,
  isLoading = false,
  noDataMessage = "No results found.",
  containerClassName = "",
  // Server-side specific props
  pageCount: controlledPageCount,
  pagination: controlledPagination,
  onPaginationChange: setControlledPagination,
  sorting: controlledSorting,
  onSortingChange: setControlledSorting,
  rowSelection: controlledRowSelection,
  onRowSelectionChange: setControlledRowSelection,
  /** Enables the right-click context menu on table rows. */
  enableRowContextMenu = false,
  /** A render prop function that returns the context menu JSX. Receives the `row` object and a `closeMenu` function. */
  renderRowContextMenu = () => null,
}) {
  // State for client-side operations
  const [globalFilter, setGlobalFilter] = useState("");
  const [internalSorting, setInternalSorting] = useState([]);
  const [internalPagination, setInternalPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  const [internalRowSelection, setInternalRowSelection] = useState({});

  const scrollContainerRef = useRef(null);
  const [scrollPosition, setScrollPosition] = useState({
    isAtLeftEdge: true,
    isAtRightEdge: false,
  });

  const handleScroll = () => {
    const el = scrollContainerRef.current;
    if (!el) return;

    const newIsAtLeftEdge = el.scrollLeft < 1;
    const newIsAtRightEdge =
      el.scrollWidth - el.scrollLeft - el.clientWidth < 1;

    // Only update state if the edge status has actually changed to prevent excessive re-renders
    if (
      newIsAtLeftEdge !== scrollPosition.isAtLeftEdge ||
      newIsAtRightEdge !== scrollPosition.isAtRightEdge
    ) {
      setScrollPosition({
        isAtLeftEdge: newIsAtLeftEdge,
        isAtRightEdge: newIsAtRightEdge,
      });
    }
  };

  // Effect to check scroll position on mount, resize, and data changes
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      handleScroll(); // Initial check
      window.addEventListener("resize", handleScroll);
    }
    return () => {
      if (container) {
        window.removeEventListener("resize", handleScroll);
      }
    };
  }, [data, columns]);

  // STEP 1: Create the single, authoritative list of columns.
  const finalColumns = useMemo(() => {
    if (!enableRowSelection) return columns;
    const selectionColumn = {
      id: "select",
      meta: { isPinned: "left" },
      size: 45,
      header: ({ table }) => (
        <input
          type="checkbox"
          className="h-4 w-4"
          checked={table.getIsAllPageRowsSelected()}
          onChange={table.getToggleAllPageRowsSelectedHandler()}
        />
      ),
      cell: ({ row }) => (
        <input
          type="checkbox"
          className="h-4 w-4"
          checked={row.getIsSelected()}
          onChange={row.getToggleSelectedHandler()}
        />
      ),
    };
    return [selectionColumn, ...columns];
  }, [columns, enableRowSelection]);

  // STEP 2: Derive the pinning state from the final, authoritative column list.
  const [columnPinning, setColumnPinning] = useState(() => {
    const left = finalColumns
      .filter((c) => c.meta?.isPinned === "left")
      .map((c) => c.id || c.accessorKey);
    const right = finalColumns
      .filter((c) => c.meta?.isPinned === "right")
      .map((c) => c.id || c.accessorKey);
    return { left, right };
  });

  // Determine which state and handlers to use (controlled vs. internal)
  const isServerPagination = paginationMode === "server";
  const isServerSorting = !!setControlledSorting;
  const isServerSelection = !!setControlledRowSelection;

  const pagination = isServerPagination
    ? controlledPagination
    : internalPagination;
  const onPaginationChange = isServerPagination
    ? setControlledPagination
    : setInternalPagination;
  const sorting = isServerSorting ? controlledSorting : internalSorting;
  const onSortingChange = isServerSorting
    ? setControlledSorting
    : setInternalSorting;
  const rowSelection = isServerSelection
    ? controlledRowSelection
    : internalRowSelection;
  const onRowSelectionChange = isServerSelection
    ? setControlledRowSelection
    : setInternalRowSelection;

  const table = useReactTable({
    data,
    columns: finalColumns,
    pageCount: controlledPageCount,
    state: {
      globalFilter,
      sorting,
      pagination,
      rowSelection,
      columnPinning, // Pass the controlled pinning state
    },
    // Models
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    // Handlers
    onGlobalFilterChange: setGlobalFilter,
    onSortingChange,
    onPaginationChange,
    onRowSelectionChange,
    onColumnPinningChange: setColumnPinning,
    // Options
    enableColumnPinning: true,
    manualPagination: isServerPagination,
    manualSorting: isServerSorting,
  });

  const getPinnedShadowClass = (column) => {
    const isPinned = column.getIsPinned();
    if (!isPinned) return "";

    const isLastLeft = isPinned === "left" && column.getIsLastColumn("left");
    const isFirstRight =
      isPinned === "right" && column.getIsFirstColumn("right");

    // Apply shadow only if NOT at the respective edge
    if (isLastLeft && !scrollPosition.isAtLeftEdge) {
      return "shadow-pinned-right";
    }
    if (isFirstRight && !scrollPosition.isAtRightEdge) {
      return "shadow-pinned-left";
    }

    return "";
  };

  const getCommonPinningStyles = (column) => {
    const isPinned = column.getIsPinned();
    if (!isPinned) return {};
    const offset = column.getStart(isPinned);
    return { position: "sticky", [isPinned]: `${offset}px`, zIndex: 1 };
  };

  // --- NEW STATE FOR CONTEXT MENU ---
  const [contextMenu, setContextMenu] = useState({
    visible: false,
    x: 0,
    y: 0,
    row: null,
  });

  const handleContextMenu = (event, row) => {
    if (!enableRowContextMenu) return;
    event.preventDefault();
    setContextMenu({
      visible: true,
      x: event.clientX,
      y: event.clientY,
      row: row,
    });
  };

  const closeMenu = () => {
    setContextMenu((prev) => ({ ...prev, visible: false }));
  };

  // Effect to handle closing the menu when clicking outside or pressing Escape
  useEffect(() => {
    if (!contextMenu.visible) return;

    const handleClickOutside = () => closeMenu();
    const handleEscape = (e) => {
      if (e.key === "Escape") closeMenu();
    };

    document.addEventListener("click", handleClickOutside);
    document.addEventListener("keydown", handleEscape);

    return () => {
      document.removeEventListener("click", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [contextMenu.visible]);

  return (
    <div className={`flex flex-col gap-4 ${containerClassName}`}>
      {/* --- Toolbar --- */}
      {(enableGlobalFilter || toolbarContent) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {enableGlobalFilter && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={globalFilter ?? ""}
                  onChange={(e) => setGlobalFilter(e.target.value)}
                  placeholder="Search..."
                  className="pl-9 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 focus:ring-2 focus:ring-indigo-500 focus:outline-none text-sm"
                />
              </div>
            )}
          </div>
          {toolbarContent}
        </div>
      )}

      {/* --- Table --- */}
      <div className="rounded-lg border dark:border-gray-700 shadow-sm">
        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className={`overflow-auto relative scrollbar-thin scrollbar-track-transparent scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 hover:scrollbar-thumb-gray-400 dark:hover:scrollbar-thumb-gray-500 ${
            fixedHeader ? "max-h-[600px]" : ""
          }`}
        >
          <table
            className="w-full border-collapse text-left"
            style={{ minWidth: table.getTotalSize(), tableLayout: "fixed" }}
          >
            <thead
              className={`text-xs text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/50 ${
                fixedHeader ? "sticky top-0 z-20" : ""
              }`}
            >
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th
                      key={header.id}
                      className={`px-4 py-3 font-medium whitespace-nowrap bg-gray-50 dark:bg-gray-800/50 ${getPinnedShadowClass(
                        header.column
                      )}`}
                      style={{
                        width: header.getSize(),
                        ...getCommonPinningStyles(header.column),
                      }}
                    >
                      {header.isPlaceholder ? null : (
                        <div
                          className={`flex items-center gap-2 ${
                            header.column.getCanSort()
                              ? "cursor-pointer select-none"
                              : ""
                          }`}
                          onClick={header.column.getToggleSortingHandler()}
                        >
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                          {header.column.getCanSort() && (
                            <ArrowUpDown className="h-3 w-3 text-gray-400" />
                          )}
                        </div>
                      )}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {isLoading ? (
                Array.from({ length: pagination?.pageSize ?? 10 }).map(
                  (_, i) => (
                    <tr
                      key={i}
                      className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 animate-pulse"
                    >
                      {finalColumns.map((col, j) => (
                        <td key={j} className="px-6 py-4">
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                        </td>
                      ))}
                    </tr>
                  )
                )
              ) : table.getRowModel().rows.length > 0 ? (
                table.getRowModel().rows.map((row) => (
                  <tr
                    key={row.id}
                    // --- DESIGN ENHANCEMENT: Zebra striping and smoother transitions ---
                    className={`border-b dark:border-gray-700/50 transition-colors duration-150 ease-in-out even:bg-gray-50/50 dark:even:bg-white/[.02] hover:bg-gray-100/50 dark:hover:bg-white/[.05] ${
                      row.getIsSelected()
                        ? "!bg-indigo-50 dark:!bg-indigo-900/20"
                        : ""
                    }`}
                    //onContextMenu={(e) => handleContextMenu(e, row)}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <td
                        key={cell.id}
                        className={`px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300 ${getPinnedShadowClass(
                          cell.column
                        )} ${
                          cell.column.getIsPinned()
                            ? row.getIsSelected()
                              ? "bg-indigo-50 dark:bg-indigo-900/20"
                              : "bg-white dark:bg-gray-900"
                            : "bg-transparent"
                        }`}
                        style={{
                          width: cell.column.getSize(),
                          ...getCommonPinningStyles(cell.column),
                        }}
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </td>
                    ))}
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={finalColumns.length}
                    className="px-6 py-10 text-center"
                  >
                    {noDataMessage}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* --- DESIGN ENHANCEMENT: Polished pagination with icons --- */}
      {table.getPageCount() > 1 && (
        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 pt-2">
          <div className="flex-1">
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-2">
              <span>Rows:</span>
              <select
                value={table.getState().pagination.pageSize}
                onChange={(e) => table.setPageSize(Number(e.target.value))}
                className="border border-gray-300 dark:border-gray-600 rounded-md p-1 bg-white dark:bg-gray-700 focus:ring-1 focus:ring-indigo-500 focus:outline-none"
              >
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <span>
              Page{" "}
              <span className="font-semibold text-gray-800 dark:text-gray-200">
                {table.getState().pagination.pageIndex + 1} of{" "}
                {table.getPageCount()}
              </span>
            </span>
            <div className="flex items-center gap-1">
              {[
                {
                  icon: ChevronsLeft,
                  onClick: () => table.setPageIndex(0),
                  disabled: !table.getCanPreviousPage(),
                },
                {
                  icon: ChevronLeft,
                  onClick: () => table.previousPage(),
                  disabled: !table.getCanPreviousPage(),
                },
                {
                  icon: ChevronRight,
                  onClick: () => table.nextPage(),
                  disabled: !table.getCanNextPage(),
                },
                {
                  icon: ChevronsRight,
                  onClick: () => table.setPageIndex(table.getPageCount() - 1),
                  disabled: !table.getCanNextPage(),
                },
              ].map((btn, i) => (
                <button
                  key={i}
                  className="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-transparent"
                  onClick={btn.onClick}
                  disabled={btn.disabled}
                >
                  <btn.icon className="h-4 w-4" />
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
      {/* --- RENDER THE CONTEXT MENU --- */}
      {contextMenu.visible && contextMenu.row && (
        <div
          style={{ top: contextMenu.y, left: contextMenu.x }}
          className="fixed z-50"
          // Stop propagation to prevent the 'click outside' handler from closing the menu immediately
          onClick={(e) => e.stopPropagation()}
        >
          {/* Call the user-provided render prop */}
          {renderRowContextMenu(contextMenu.row, closeMenu)}
        </div>
      )}
    </div>
  );
}

// PropTypes for runtime type checking
CommonTable.propTypes = {
  data: PropTypes.array.isRequired,
  columns: PropTypes.array.isRequired,
  paginationMode: PropTypes.oneOf(["client", "server"]),
  enableGlobalFilter: PropTypes.bool,
  enableRowSelection: PropTypes.bool,
  fixedHeader: PropTypes.bool,
  toolbarContent: PropTypes.node,
  isLoading: PropTypes.bool,
  noDataMessage: PropTypes.string,
  containerClassName: PropTypes.string,
  pageCount: PropTypes.number,
  pagination: PropTypes.object,
  onPaginationChange: PropTypes.func,
  sorting: PropTypes.array,
  onSortingChange: PropTypes.func,
  rowSelection: PropTypes.object,
  onRowSelectionChange: PropTypes.func,
  enableRowContextMenu: PropTypes.bool,
  renderRowContextMenu: PropTypes.func,
};
