// src/components/table/CommonTable.jsx

import React from "react";
import {
  useTableState,
  useTableScroll,
  useContextMenu,
  useTableConfiguration,
} from "./hooks";
import {
  TableToolbar,
  TableHeader,
  TableBody,
  TablePagination,
  ContextMenu,
} from "./components";
import { TABLE_CLASSES } from "./constants/tableConstants";
import { TablePropTypes } from "./types/tableTypes";

/**
 * A highly reusable and configurable table component built with @tanstack/react-table and Tailwind CSS.
 * It supports client/server-side pagination, sorting, global filtering, row selection, and column pinning.
 */
export function CommonTable({
  data,
  columns,
  paginationMode = "client",
  enableGlobalFilter = false,
  enableRowSelection = false,
  fixedHeader = false,
  toolbarContent,
  isLoading = false,
  noDataMessage = "No results found.",
  containerClassName = "",
  // Server-side specific props
  pageCount: controlledPageCount,
  pagination: controlledPagination,
  onPaginationChange: setControlledPagination,
  sorting: controlledSorting,
  onSortingChange: setControlledSorting,
  rowSelection: controlledRowSelection,
  onRowSelectionChange: setControlledRowSelection,
  /** Enables the right-click context menu on table rows. */
  enableRowContextMenu = false,
  /** A render prop function that returns the context menu JSX. Receives the `row` object and a `closeMenu` function. */
  renderRowContextMenu = () => null,
}) {
  // Use custom hooks for state management
  const tableState = useTableState({
    paginationMode,
    controlledPagination,
    setControlledPagination,
    controlledSorting,
    setControlledSorting,
    controlledRowSelection,
    setControlledRowSelection,
  });

  const { scrollContainerRef, scrollPosition, handleScroll } = useTableScroll(
    data,
    columns
  );

  const { contextMenu, handleContextMenu, closeMenu } =
    useContextMenu(enableRowContextMenu);

  const { table, finalColumns } = useTableConfiguration({
    data,
    columns,
    enableRowSelection,
    tableState,
    controlledPageCount,
  });

  return (
    <div className={`${TABLE_CLASSES.container} ${containerClassName}`}>
      <TableToolbar
        enableGlobalFilter={enableGlobalFilter}
        globalFilter={tableState.globalFilter}
        onGlobalFilterChange={tableState.onGlobalFilterChange}
        toolbarContent={toolbarContent}
      />

      <div className={TABLE_CLASSES.tableWrapper}>
        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className={`${TABLE_CLASSES.scrollContainer} ${
            fixedHeader ? TABLE_CLASSES.fixedHeaderContainer : ""
          }`}
        >
          <table
            className={TABLE_CLASSES.table}
            style={{ minWidth: table.getTotalSize(), tableLayout: "fixed" }}
          >
            <TableHeader
              table={table}
              fixedHeader={fixedHeader}
              scrollPosition={scrollPosition}
            />
            <TableBody
              table={table}
              finalColumns={finalColumns}
              isLoading={isLoading}
              noDataMessage={noDataMessage}
              scrollPosition={scrollPosition}
              onContextMenu={handleContextMenu}
            />
          </table>
        </div>
      </div>

      <TablePagination table={table} />

      <ContextMenu
        contextMenu={contextMenu}
        renderRowContextMenu={renderRowContextMenu}
        closeMenu={closeMenu}
      />
    </div>
  );
}

// PropTypes for runtime type checking
CommonTable.propTypes = TablePropTypes;
