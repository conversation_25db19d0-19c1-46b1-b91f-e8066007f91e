// src/components/table/test/CommonTableTest.jsx

import React from "react";
import { CommonTable } from "../CommonTable";

// Simple test data
const testData = [
  { id: 1, name: "<PERSON>", email: "<EMAIL>", status: "Active" },
  { id: 2, name: "<PERSON>", email: "<EMAIL>", status: "Inactive" },
  { id: 3, name: "<PERSON>", email: "<EMAIL>", status: "Active" },
];

// Simple test columns
const testColumns = [
  {
    accessorKey: "id",
    header: "ID",
    size: 60,
  },
  {
    accessorKey: "name",
    header: "Name",
    size: 200,
  },
  {
    accessorKey: "email",
    header: "Email",
    size: 250,
  },
  {
    accessorKey: "status",
    header: "Status",
    size: 100,
  },
];

/**
 * Simple test component to verify the refactored CommonTable works correctly
 */
export function CommonTableTest() {
  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">CommonTable Refactoring Test</h1>
      <CommonTable
        data={testData}
        columns={testColumns}
        paginationMode="client"
        enableGlobalFilter={true}
        enableRowSelection={true}
        fixedHeader={false}
        toolbarContent={
          <button className="bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700">
            Test Button
          </button>
        }
      />
    </div>
  );
}
