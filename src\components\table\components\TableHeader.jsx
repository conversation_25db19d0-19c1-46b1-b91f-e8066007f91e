// src/components/table/components/TableHeader.jsx

import React from "react";
import PropTypes from "prop-types";
import { flexRender } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";
import {
  getPinnedShadowClass,
  getCommonPinningStyles,
} from "../utils/tableUtils";
import { TABLE_CLASSES } from "../constants/tableConstants";
import { ScrollPositionPropType } from "../types/tableTypes";

/**
 * Table header component
 */
export function TableHeader({ table, fixedHeader, scrollPosition }) {
  const headerClasses = `${TABLE_CLASSES.thead} ${
    fixedHeader ? TABLE_CLASSES.fixedHeader : ""
  }`;

  return (
    <thead className={headerClasses}>
      {table.getHeaderGroups().map((headerGroup) => (
        <tr key={headerGroup.id}>
          {headerGroup.headers.map((header) => (
            <th
              key={header.id}
              className={`${TABLE_CLASSES.th} ${getPinnedShadowClass(
                header.column,
                scrollPosition
              )}`}
              style={{
                width: header.getSize(),
                ...getCommonPinningStyles(header.column),
              }}
            >
              {header.isPlaceholder ? null : (
                <div
                  className={`${TABLE_CLASSES.headerContent} ${
                    header.column.getCanSort()
                      ? TABLE_CLASSES.sortableHeader
                      : ""
                  }`}
                  onClick={header.column.getToggleSortingHandler()}
                >
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext()
                  )}
                  {header.column.getCanSort() && (
                    <ArrowUpDown className={TABLE_CLASSES.sortIcon} />
                  )}
                </div>
              )}
            </th>
          ))}
        </tr>
      ))}
    </thead>
  );
}

TableHeader.propTypes = {
  table: PropTypes.object.isRequired,
  fixedHeader: PropTypes.bool,
  scrollPosition: ScrollPositionPropType.isRequired,
};
