import React from "react";
import RcTailwindTable from "./components/table/rc-tailwind-table";
import ClientSideUserTable from "./features/users/ClientSideUserTable";
import ServerSideUserTable from "./features/users/ServerSideUserTable";
import { Eye, Edit, Trash2, Copy } from "lucide-react"; // Some nice icons

const ContextMenuItem = ({ icon, label, onClick }) => (
  <button
    onClick={onClick}
    className="flex items-center w-full px-4 py-2 text-sm text-left text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
  >
    {icon}
    <span className="ml-2">{label}</span>
  </button>
);

const App = () => {
  // Define the function that will render our context menu
  const renderUserContextMenu = (row, closeMenu) => {
    const user = row.original; // Get the original data object for the row

    // Example actions
    const handleView = () => {
      alert(`Viewing user: ${user.firstName} (ID: ${user.id})`);
      closeMenu();
    };

    const handleEdit = () => {
      alert(`Editing user: ${user.firstName} (ID: ${user.id})`);
      closeMenu();
    };

    const handleDelete = () => {
      if (
        window.confirm(`Are you sure you want to delete ${user.firstName}?`)
      ) {
        alert(`Deleted user: ${user.firstName} (ID: ${user.id})`);
        closeMenu();
      }
    };

    const handleCopyId = () => {
      navigator.clipboard.writeText(user.id);
      alert(`Copied ID: ${user.id}`);
      closeMenu();
    };

    // Return the JSX for the menu
    return (
      <div className="w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg border dark:border-gray-600 py-1">
        <ContextMenuItem
          icon={<Eye size={16} />}
          label="View Details"
          onClick={handleView}
        />
        <ContextMenuItem
          icon={<Edit size={16} />}
          label="Edit User"
          onClick={handleEdit}
        />
        <ContextMenuItem
          icon={<Copy size={16} />}
          label="Copy User ID"
          onClick={handleCopyId}
        />
        <div className="my-1 h-px bg-gray-200 dark:bg-gray-700" />
        <ContextMenuItem
          icon={<Trash2 size={16} className="text-red-500" />}
          label="Delete User"
          onClick={handleDelete}
        />
      </div>
    );
  };

  return (
    <div>
      {/* <RcTailwindTable /> */}
      <ClientSideUserTable
        enableRowContextMenu={true}
        renderRowContextMenu={renderUserContextMenu}
      />
      <ServerSideUserTable />
    </div>
  );
};

export default App;

// src/App.jsx
// import React, { useMemo, useState } from "react";
// import {
//   useReactTable,
//   getCoreRowModel,
//   flexRender,
// } from "@tanstack/react-table";

// // STEP 1: DEFINE DATA AND COLUMNS
// // =======================================================
// const defaultData = [
//   {
//     id: 1,
//     firstName: "Tanner",
//     lastName: "Linsley",
//     age: 33,
//     visits: 100,
//     status: "In Relationship",
//     progress: 50,
//   },
//   {
//     id: 2,
//     firstName: "Kevin",
//     lastName: "Vonderheyde",
//     age: 29,
//     visits: 200,
//     status: "Single",
//     progress: 25,
//   },
//   {
//     id: 3,
//     firstName: "John",
//     lastName: "Doe",
//     age: 45,
//     visits: 150,
//     status: "Complicated",
//     progress: 75,
//   },
//   {
//     id: 4,
//     firstName: "Jane",
//     lastName: "Smith",
//     age: 28,
//     visits: 50,
//     status: "In Relationship",
//     progress: 90,
//   },
// ];

// const defaultColumns = [
//   // This is a "display" column for the checkbox

//   {
//     accessorKey: "id",
//     header: "ID",
//     meta: { isPinned: "left" },
//     size: 60, // CRUCIAL: Every pinned column MUST have a size
//   },
//   { accessorKey: "firstName", header: "First Name", size: 150 },
//   { accessorKey: "lastName", header: "Last Name", size: 150 },
//   { accessorKey: "age", header: "Age", size: 80 },
//   { accessorKey: "visits", header: "Visits", size: 100 },
//   { accessorKey: "status", header: "Status", size: 150 },
//   { accessorKey: "progress", header: "Profile Progress", size: 180 },
//   {
//     id: "actions",
//     header: "Actions",
//     meta: { isPinned: "right" },
//     size: 80, // CRUCIAL: Every pinned column MUST have a size
//     cell: () => <button className="text-blue-500">Edit</button>,
//   },
// ];

// // STEP 2: THE MINIMAL, BULLETPROOF TABLE COMPONENT
// // =======================================================
// function MinimalPinningTable({ data, columns, enableRowSelection = true }) {
//   const finalColumns = useMemo(() => {
//     // This is the column that will be programmatically added.
//     const selectionColumn = {
//       id: "select",
//       // CRUCIAL: It defines its own pinning behavior in its metadata.
//       meta: { isPinned: "left" },
//       size: 45,
//       header: ({ table }) => (
//         <input
//           type="checkbox"
//           className="h-4 w-4"
//           checked={table.getIsAllPageRowsSelected()}
//           onChange={table.getToggleAllPageRowsSelectedHandler()}
//         />
//       ),
//       cell: ({ row }) => (
//         <input
//           type="checkbox"
//           className="h-4 w-4"
//           checked={row.getIsSelected()}
//           onChange={row.getToggleSelectedHandler()}
//         />
//       ),
//     };

//     // Return either the original columns, or the columns with the selection column prepended.
//     return enableRowSelection ? [selectionColumn, ...columns] : columns;
//   }, [columns, enableRowSelection]);

//   const [columnPinning, setColumnPinning] = useState(() => {
//     // No more hard-coding 'select'! We read the meta property from our final list.
//     const left = finalColumns
//       .filter((c) => c.meta?.isPinned === "left")
//       .map((c) => c.id || c.accessorKey);

//     const right = finalColumns
//       .filter((c) => c.meta?.isPinned === "right")
//       .map((c) => c.id || c.accessorKey);

//     return { left, right };
//   });

//   const table = useReactTable({
//     data,
//     columns: finalColumns,
//     // Key settings for pinning to work
//     state: {
//       columnPinning,
//     },
//     onColumnPinningChange: setColumnPinning,
//     // This top-level option is still required
//     enableColumnPinning: true,
//     getCoreRowModel: getCoreRowModel(),
//   });

//   // Helper to get the required styles for a pinned column
//   const getCommonPinningStyles = (column) => {
//     const isPinned = column.getIsPinned();
//     if (!isPinned) return {};

//     const offset = column.getStart(isPinned); // Pass the pin direction

//     return {
//       position: "sticky",
//       [isPinned]: `${offset}px`, // Dynamically set 'left' or 'right'
//       zIndex: 1,
//     };
//   };

//   return (
//     // This is the SCROLLING CONTAINER. It MUST have overflow.
//     <div className="overflow-x-auto relative border rounded-lg max-h-[500px]">
//       <table
//         className="border-collapse"
//         // This makes the table wide enough to scroll
//         style={{ minWidth: table.getTotalSize() }}
//       >
//         <thead className="bg-gray-100">
//           {table.getHeaderGroups().map((headerGroup) => (
//             <tr key={headerGroup.id}>
//               {headerGroup.headers.map((header) => (
//                 <th
//                   key={header.id}
//                   className="px-4 py-2 text-left font-bold whitespace-nowrap bg-gray-100" // Background is essential
//                   // Apply width and pinning styles
//                   style={{
//                     width: header.getSize(),
//                     ...getCommonPinningStyles(header.column),
//                   }}
//                 >
//                   {flexRender(
//                     header.column.columnDef.header,
//                     header.getContext()
//                   )}
//                 </th>
//               ))}
//             </tr>
//           ))}
//         </thead>
//         <tbody>
//           {table.getRowModel().rows.map((row) => (
//             <tr
//               key={row.id}
//               className="border-t hover:bg-gray-50 dark:hover:bg-gray-800"
//             >
//               {row.getVisibleCells().map((cell) => {
//                 const isPinned = cell.column.getIsPinned();
//                 return (
//                   <td
//                     key={cell.id}
//                     // ===================================================================
//                     // FIX 2: Pinned body cells MUST have a solid background color
//                     // ===================================================================
//                     className={`px-4 py-2 whitespace-nowrap
//                     ${
//                       isPinned
//                         ? "bg-white dark:bg-gray-900" // Matches the base row color
//                         : "bg-transparent" // Non-pinned cells are transparent to allow hover on <tr>
//                     }`}
//                     style={{
//                       width: cell.column.getSize(),
//                       ...getCommonPinningStyles(cell.column),
//                     }}
//                   >
//                     {flexRender(cell.column.columnDef.cell, cell.getContext())}
//                   </td>
//                 );
//               })}
//             </tr>
//           ))}
//         </tbody>
//       </table>
//     </div>
//   );
// }

// // STEP 3: THE APP TO RENDER IT ALL
// // =======================================================
// function App() {
//   return (
//     <div className="p-8 bg-gray-50 min-h-screen">
//       <h1 className="text-3xl font-bold mb-6">Column Pinning Test</h1>
//       <p className="mb-4">
//         If this table works, the issue is in your application's CSS. If it does
//         NOT work, there might be a problem with your project setup (e.g.,
//         Tailwind not processing styles).
//       </p>

//       <MinimalPinningTable data={defaultData} columns={defaultColumns} />

//       <div className="mt-8 p-4 border-l-4 border-red-500 bg-red-50">
//         <h2 className="font-bold text-red-800">
//           If Pinning Still Fails: Debug Checklist
//         </h2>
//         <ol className="list-decimal list-inside mt-2 text-red-700">
//           <li>
//             Open your browser's Developer Tools (F12 or Right-Click - Inspect).
//           </li>
//           <li>
//             Find the `div` that has `overflow-x-auto`. This is our scrolling
//             container.
//           </li>
//           <li>
//             Starting from that `div`, go UP the DOM tree one parent at a time.
//           </li>
//           <li>
//             On EACH parent element, check the "Computed" styles tab in your
//             DevTools.
//           </li>
//           <li>
//             Look for `overflow`, `overflow-x`, or `overflow-y`. If you find ANY
//             parent with a value of `hidden`, `clip`, or `scroll` (other than our
//             intended scroller), you have found the problem. That element is
//             breaking the `sticky` positioning context.
//           </li>
//         </ol>
//       </div>
//     </div>
//   );
// }

// export default App;
