// src/components/table/hooks/useTableScroll.js

import { useState, useEffect, useRef, useCallback } from "react";

/**
 * Custom hook to manage table scroll behavior and edge detection
 * Used for column pinning shadow effects
 */
export function useTableScroll(data, columns) {
  const scrollContainerRef = useRef(null);
  const [scrollPosition, setScrollPosition] = useState({
    isAtLeftEdge: true,
    isAtRightEdge: false,
  });

  const handleScroll = useCallback(() => {
    const el = scrollContainerRef.current;
    if (!el) return;

    const newIsAtLeftEdge = el.scrollLeft < 1;
    const newIsAtRightEdge =
      el.scrollWidth - el.scrollLeft - el.clientWidth < 1;

    // Only update state if the edge status has actually changed to prevent excessive re-renders
    if (
      newIsAtLeftEdge !== scrollPosition.isAtLeftEdge ||
      newIsAtRightEdge !== scrollPosition.isAtRightEdge
    ) {
      setScrollPosition({
        isAtLeftEdge: newIsAtLeftEdge,
        isAtRightEdge: newIsAtRightEdge,
      });
    }
  }, [scrollPosition.isAtLeftEdge, scrollPosition.isAtRightEdge]);

  // Effect to check scroll position on mount, resize, and data changes
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      handleScroll(); // Initial check
      window.addEventListener("resize", handleScroll);
    }
    return () => {
      if (container) {
        window.removeEventListener("resize", handleScroll);
      }
    };
  }, [data, columns, handleScroll]);

  return {
    scrollContainerRef,
    scrollPosition,
    handleScroll,
  };
}
