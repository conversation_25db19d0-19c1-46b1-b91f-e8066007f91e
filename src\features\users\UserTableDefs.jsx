export const userColumns = [
  // The selection column is added automatically by CommonTable
  {
    accessorKey: "id",
    header: "ID",
    meta: { isPinned: "left" },
    size: 60,
  },
  {
    accessorKey: "name",
    header: "NAME", // Using uppercase to match image style
    size: 200,
  },
  {
    accessorKey: "email",
    header: "EMAIL ADDRESS",
    size: 250,
  },
  {
    accessorKey: "role",
    header: "ROLE",
    size: 120,
  },
  {
    accessorKey: "status",
    header: "STATUS",
    size: 100,
    // Custom cell renderer for the status "pill"
    cell: ({ getValue }) => {
      const status = getValue();
      const color =
        status === "Active"
          ? "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300"
          : "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300";
      return (
        <span
          className={`px-2.5 py-1 rounded-full text-xs font-medium ${color}`}
        >
          {status}
        </span>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: "CREATED AT",
    size: 150,
    cell: ({ getValue }) => new Date(getValue()).toLocaleDateString(),
  },
  {
    id: "actions",
    header: "ACTIONS",
    meta: { isPinned: "right" },
    size: 80,
    cell: ({ row }) => (
      <button className="font-medium text-indigo-600 dark:text-indigo-400 hover:underline">
        Edit
      </button>
    ),
  },
];
