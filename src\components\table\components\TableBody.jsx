// src/components/table/components/TableBody.jsx

import React from "react";
import PropTypes from "prop-types";
import { flexRender } from "@tanstack/react-table";
import {
  generateLoadingRows,
  getRowClassName,
  getCellClassName,
  getCommonPinningStyles,
} from "../utils/tableUtils";

/**
 * Table body component
 */
export function TableBody({
  table,
  finalColumns,
  isLoading,
  noDataMessage,
  scrollPosition,
  onContextMenu,
}) {
  if (isLoading) {
    const { pagination } = table.getState();
    return (
      <tbody>
        {generateLoadingRows(pagination?.pageSize ?? 10, finalColumns.length)}
      </tbody>
    );
  }

  if (table.getRowModel().rows.length === 0) {
    return (
      <tbody>
        <tr>
          <td colSpan={finalColumns.length} className="px-6 py-10 text-center">
            {noDataMessage}
          </td>
        </tr>
      </tbody>
    );
  }

  return (
    <tbody>
      {table.getRowModel().rows.map((row) => (
        <tr
          key={row.id}
          className={getRowClassName(row)}
          onContextMenu={(e) => onContextMenu(e, row)}
        >
          {row.getVisibleCells().map((cell) => (
            <td
              key={cell.id}
              className={getCellClassName(cell, row, scrollPosition)}
              style={{
                width: cell.column.getSize(),
                ...getCommonPinningStyles(cell.column),
              }}
            >
              {flexRender(cell.column.columnDef.cell, cell.getContext())}
            </td>
          ))}
        </tr>
      ))}
    </tbody>
  );
}

TableBody.propTypes = {
  table: PropTypes.object.isRequired,
  finalColumns: PropTypes.array.isRequired,
  isLoading: PropTypes.bool,
  noDataMessage: PropTypes.string,
  scrollPosition: PropTypes.shape({
    isAtLeftEdge: PropTypes.bool,
    isAtRightEdge: PropTypes.bool,
  }).isRequired,
  onContextMenu: PropTypes.func,
};
