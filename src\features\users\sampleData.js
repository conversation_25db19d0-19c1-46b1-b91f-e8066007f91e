// src/features/users/sampleData.js

/**
 * This file contains mock data for the user table examples.
 * In a real application, you would fetch this data from an API.
 * For more complex or realistic mock data, consider using a library like @faker-js/faker.
 */

const firstNames = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];
const lastNames = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];
const roles = ["<PERSON><PERSON>", "<PERSON><PERSON>", "Editor"];
const statuses = ["Active", "Inactive"];

/**
 * Generates an array of 100 sample user objects.
 */
export const sampleUserData = Array.from({ length: 100 }, (_, index) => {
  const id = index + 1;
  const firstName = firstNames[index % firstNames.length];
  const lastName = lastNames[index % lastNames.length];
  const name = `${firstName} ${lastName}`;
  const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${id}@example.com`;

  // Create a date that goes back in time from today
  const createdAtDate = new Date();
  createdAtDate.setDate(createdAtDate.getDate() - (100 - index)); // Spread creation dates over the last 100 days

  return {
    id: id,
    name: name,
    email: email,
    role: roles[id % roles.length],
    status: statuses[id % statuses.length],
    createdAt: createdAtDate.toISOString(),
  };
});
