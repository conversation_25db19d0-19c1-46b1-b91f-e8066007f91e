{"name": "rc-comp", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "@tanstack/react-table": "^8.21.3", "lucide-react": "^0.525.0", "prop-types": "^15.8.1", "rc-pagination": "^5.1.0", "rc-table": "^7.51.1", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss-animate": "^1.0.7", "vite": "^7.0.0"}}