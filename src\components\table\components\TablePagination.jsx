// src/components/table/components/TablePagination.jsx

import React from "react";
import PropTypes from "prop-types";
import {
  ChevronsLeft,
  ChevronLeft,
  ChevronRight,
  ChevronsRight,
} from "lucide-react";
import { DEFAULT_PAGE_SIZES, PAGINATION_CLASSES } from "../constants/tableConstants";

/**
 * Table pagination component
 */
export function TablePagination({ table }) {
  if (table.getPageCount() <= 1) {
    return null;
  }

  const paginationButtons = [
    {
      icon: ChevronsLeft,
      onClick: () => table.setPageIndex(0),
      disabled: !table.getCanPreviousPage(),
    },
    {
      icon: ChevronLeft,
      onClick: () => table.previousPage(),
      disabled: !table.getCanPreviousPage(),
    },
    {
      icon: ChevronRight,
      onClick: () => table.nextPage(),
      disabled: !table.getCanNextPage(),
    },
    {
      icon: ChevronsRight,
      onClick: () => table.setPageIndex(table.getPageCount() - 1),
      disabled: !table.getCanNextPage(),
    },
  ];

  return (
    <div className={PAGINATION_CLASSES.container}>
      <div className={PAGINATION_CLASSES.selectionInfo}>
        {table.getFilteredSelectedRowModel().rows.length} of{" "}
        {table.getFilteredRowModel().rows.length} row(s) selected.
      </div>
      <div className={PAGINATION_CLASSES.controls}>
        <div className={PAGINATION_CLASSES.pageSizeContainer}>
          <span>Rows:</span>
          <select
            value={table.getState().pagination.pageSize}
            onChange={(e) => table.setPageSize(Number(e.target.value))}
            className={PAGINATION_CLASSES.pageSizeSelect}
          >
            {DEFAULT_PAGE_SIZES.map((pageSize) => (
              <option key={pageSize} value={pageSize}>
                {pageSize}
              </option>
            ))}
          </select>
        </div>
        <span>
          Page{" "}
          <span className={PAGINATION_CLASSES.pageInfo}>
            {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()}
          </span>
        </span>
        <div className={PAGINATION_CLASSES.buttonContainer}>
          {paginationButtons.map((btn, i) => (
            <button
              key={i}
              className={PAGINATION_CLASSES.button}
              onClick={btn.onClick}
              disabled={btn.disabled}
            >
              <btn.icon className={PAGINATION_CLASSES.buttonIcon} />
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}

TablePagination.propTypes = {
  table: PropTypes.object.isRequired,
};
