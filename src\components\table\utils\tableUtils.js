// src/components/table/utils/tableUtils.js

/**
 * Utility functions for table styling and behavior
 */

/**
 * Get the shadow class for pinned columns based on scroll position
 */
export const getPinnedShadowClass = (column, scrollPosition) => {
  const isPinned = column.getIsPinned();
  if (!isPinned) return "";

  const isLastLeft = isPinned === "left" && column.getIsLastColumn("left");
  const isFirstRight = isPinned === "right" && column.getIsFirstColumn("right");

  // Apply shadow only if NOT at the respective edge
  if (isLastLeft && !scrollPosition.isAtLeftEdge) {
    return "shadow-pinned-right";
  }
  if (isFirstRight && !scrollPosition.isAtRightEdge) {
    return "shadow-pinned-left";
  }

  return "";
};

/**
 * Get common pinning styles for columns
 */
export const getCommonPinningStyles = (column) => {
  const isPinned = column.getIsPinned();
  if (!isPinned) return {};
  const offset = column.getStart(isPinned);
  return { position: "sticky", [isPinned]: `${offset}px`, zIndex: 1 };
};

/**
 * Generate loading skeleton rows
 */
export const generateLoadingRows = (pageSize = 10, columnCount = 1) => {
  return Array.from({ length: pageSize }).map((_, i) => (
    <tr
      key={i}
      className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 animate-pulse"
    >
      {Array.from({ length: columnCount }).map((_, j) => (
        <td key={j} className="px-6 py-4">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        </td>
      ))}
    </tr>
  ));
};

/**
 * Get row className based on selection and hover states
 */
export const getRowClassName = (row) => {
  const baseClasses = "border-b dark:border-gray-700/50 transition-colors duration-150 ease-in-out even:bg-gray-50/50 dark:even:bg-white/[.02] hover:bg-gray-100/50 dark:hover:bg-white/[.05]";
  const selectedClasses = row.getIsSelected() ? "!bg-indigo-50 dark:!bg-indigo-900/20" : "";
  return `${baseClasses} ${selectedClasses}`;
};

/**
 * Get cell className for pinned columns with proper background
 */
export const getCellClassName = (cell, row, scrollPosition) => {
  const baseClasses = "px-4 py-3 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300";
  const shadowClass = getPinnedShadowClass(cell.column, scrollPosition);
  
  let backgroundClass = "bg-transparent";
  if (cell.column.getIsPinned()) {
    backgroundClass = row.getIsSelected()
      ? "bg-indigo-50 dark:bg-indigo-900/20"
      : "bg-white dark:bg-gray-900";
  }
  
  return `${baseClasses} ${shadowClass} ${backgroundClass}`;
};
