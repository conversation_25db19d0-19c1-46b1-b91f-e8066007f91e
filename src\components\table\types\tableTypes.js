// src/components/table/types/tableTypes.js

import PropTypes from "prop-types";

/**
 * Common PropTypes definitions for table components
 */

export const TablePropTypes = {
  data: PropTypes.array.isRequired,
  columns: PropTypes.array.isRequired,
  paginationMode: PropTypes.oneOf(["client", "server"]),
  enableGlobalFilter: PropTypes.bool,
  enableRowSelection: PropTypes.bool,
  fixedHeader: PropTypes.bool,
  toolbarContent: PropTypes.node,
  isLoading: PropTypes.bool,
  noDataMessage: PropTypes.string,
  containerClassName: PropTypes.string,
  pageCount: PropTypes.number,
  pagination: PropTypes.object,
  onPaginationChange: PropTypes.func,
  sorting: PropTypes.array,
  onSortingChange: PropTypes.func,
  rowSelection: PropTypes.object,
  onRowSelectionChange: PropTypes.func,
  enableRowContextMenu: PropTypes.bool,
  renderRowContextMenu: PropTypes.func,
};

export const ScrollPositionPropType = PropTypes.shape({
  isAtLeftEdge: PropTypes.bool,
  isAtRightEdge: PropTypes.bool,
});

export const ContextMenuPropType = PropTypes.shape({
  visible: PropTypes.bool,
  x: PropTypes.number,
  y: PropTypes.number,
  row: PropTypes.object,
});

export const TableStatePropType = PropTypes.shape({
  pagination: PropTypes.object,
  onPaginationChange: PropTypes.func,
  sorting: PropTypes.array,
  onSortingChange: PropTypes.func,
  rowSelection: PropTypes.object,
  onRowSelectionChange: PropTypes.func,
  globalFilter: PropTypes.string,
  onGlobalFilterChange: PropTypes.func,
  isServerPagination: PropTypes.bool,
  isServerSorting: PropTypes.bool,
  isServerSelection: PropTypes.bool,
});
