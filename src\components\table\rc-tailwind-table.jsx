import Pagination from "rc-pagination";
import Table from "rc-table";
import React, { useState } from "react";

// --- DATA and COLUMNS ---
const generateData = (count) => {
  const data = [];
  for (let i = 1; i <= count; i++) {
    data.push({
      key: i,
      name: `<PERSON> ${i}`,
      age: 20 + (i % 15),
      address: `London, Park Lane no. ${i}`,
      status: i % 3 === 0 ? "inactive" : "active",
    });
  }
  return data;
};

const allData = generateData(50);

const columns = [
  { title: "Name", dataIndex: "name", key: "name", width: 200 },
  { title: "Age", dataIndex: "age", key: "age", width: 100 },
  { title: "Address", dataIndex: "address", key: "address" },
  {
    title: "Status",
    dataIndex: "status",
    key: "status",
    render: (status) => {
      const statusClass =
        status === "active"
          ? "bg-green-100 text-green-800"
          : "bg-red-100 text-red-800";
      return (
        <span
          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}`}
        >
          {status}
        </span>
      );
    },
  },
];

// 2. Custom Components with Tailwind Classes
//    IMPORTANT: Remember to spread `...props` on each component
const StyledTable = (props) => (
  <table {...props} className="w-full text-sm text-left text-gray-500" />
);
const StyledHeader = (props) => (
  <thead {...props} className="text-xs text-gray-700 uppercase bg-gray-50" />
);
const StyledBody = (props) => (
  <tbody {...props} className="bg-white divide-y divide-gray-200" />
);
const StyledHeaderRow = (props) => <tr {...props} />;
const StyledHeaderCell = (props) => (
  <th {...props} scope="col" className="px-6 py-3" />
);
const StyledBodyRow = (props) => <tr {...props} className="hover:bg-gray-50" />;
const StyledBodyCell = (props) => <td {...props} className="px-6 py-4" />;

// 3. Create the components mapping object
const customComponents = {
  table: StyledTable,
  header: {
    wrapper: StyledHeader,
    row: StyledHeaderRow,
    cell: StyledHeaderCell,
  },
  body: {
    wrapper: StyledBody,
    row: StyledBodyRow,
    cell: StyledBodyCell,
  },
};

const RcTailwindTable = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10); // We can make this dynamic with a dropdown if needed

  // Calculate the data for the current page
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const currentData = allData.slice(startIndex, endIndex);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // --- PAGINATION ITEM RENDERER ---
  const renderPaginationItem = (current, type, element) => {
    const baseClasses =
      "flex items-center justify-center px-3 h-8 leading-tight border";
    const activeClasses =
      "z-10 text-blue-600 border-blue-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700";
    const inactiveClasses =
      "text-gray-500 bg-white border-gray-300 hover:bg-gray-100 hover:text-gray-700";

    if (type === "prev") {
      return (
        <button
          className={`${baseClasses} ml-0 rounded-l-lg ${inactiveClasses}`}
        >
          Previous
        </button>
      );
    }
    if (type === "next") {
      return (
        <button className={`${baseClasses} rounded-r-lg ${inactiveClasses}`}>
          Next
        </button>
      );
    }
    if (type === "page") {
      return (
        <button
          className={`${baseClasses} ${
            current === currentPage ? activeClasses : inactiveClasses
          }`}
        >
          {current}
        </button>
      );
    }
    if (type === "jump-prev" || type === "jump-next") {
      return <span className={`${baseClasses} ${inactiveClasses}`}>...</span>;
    }
    return element; // Default fallback
  };

  return (
    <div className="relative overflow-x-auto shadow-md sm:rounded-lg m-4">
      <Table
        columns={columns}
        data={currentData}
        components={customComponents}
        rowKey="key" // Important for React to identify rows
      />
      <nav
        className="flex items-center justify-between p-4"
        aria-label="Table navigation"
      >
        <span className="text-sm font-normal text-gray-500">
          Showing{" "}
          <span className="font-semibold text-gray-900">
            {startIndex + 1}-{Math.min(endIndex, allData.length)}
          </span>{" "}
          of{" "}
          <span className="font-semibold text-gray-900">{allData.length}</span>
        </span>
        <Pagination
          current={currentPage}
          total={allData.length}
          pageSize={pageSize}
          onChange={handlePageChange}
          itemRender={renderPaginationItem}
          className="inline-flex -space-x-px text-sm"
        />
      </nav>
    </div>
  );
};

export default RcTailwindTable;
